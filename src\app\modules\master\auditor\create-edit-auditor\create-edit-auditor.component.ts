import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup, Validators } from '@angular/forms';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/common/util';
import { ROLE } from 'app/core/constants/enum-constants';
import { AuditorIncharge } from 'app/core/models/auditor-incharge.mode';
import { Auditor } from 'app/core/models/auditor.model';
import { SharedModule } from 'app/shared/shared.module';
import {
  CommonService,
  FORM_CONTROL_TYPES,
  FormFactoryService,
  HTTP_STATUS,
  InvokeService,
  TABLE_ACTION_TYPES,
} from 'app/shared/tapas-ui';
import { environment, PARTNERS } from 'environments/environment';
import * as sha512 from 'js-sha512';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { distinctUntilChanged, forkJoin, ReplaySubject, takeUntil } from 'rxjs';

import { AUDITOR_FORM_MODEL } from './auditor-form.model';

@Component({
  selector: 'tps-create-edit-auditor',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './create-edit-auditor.component.html',
  styleUrl: './create-edit-auditor.component.scss'
})
export class CreateEditAuditorComponent implements OnInit, OnDestroy {
  public hdr: string = 'Create Auditor';
  public auditorForm: FormGroup;
  public fields: any[] = [];
  public rec: Auditor = new Auditor();
  isMobile: boolean = false;
  private allAuditors: any[] = [];
  private allAuditorInchageList: any[] = [];

  //constants
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;
  public status: string;

  //unsubscribe
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);

  constructor(
    private _commonService: CommonService,
    private _dialogConfig: DynamicDialogConfig,
    public _dialogConfigRef: DynamicDialogRef,
    private _formFactoryService: FormFactoryService,
    private _invokeService: InvokeService,
  ) { }

  ngOnInit(): void {
    // Monitor screen size changes
    this._commonService.isMobile().subscribe(result => {
      this.isMobile = result.matches;
    });

    this.status = this._dialogConfig.data?.status;
    this.buildForm();
    this.configureForm();
    this.getRequiredData();
  }

  //build form
  private buildForm() {
    const formGroupFields = this._formFactoryService.getFormControlsFields(AUDITOR_FORM_MODEL);
    this.fields = this._formFactoryService.getFieldsList(AUDITOR_FORM_MODEL);
    this.auditorForm = new FormGroup(formGroupFields);
  }

  private getRequiredData(): void {
    let auditorDetails$ = this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.auditor.getAllAuditors);
    let auditorIncharge$ = this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.auditorInCharge.get)
    forkJoin([auditorDetails$, auditorIncharge$])
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          this.allAuditors = response[0] as Auditor[];
          this.allAuditorInchageList = response[1] as AuditorIncharge[];
          this.configureInChargeList();
          this.configureForm();
        },
        error: error => {
          this._commonService.handleError(error);
        }
      })
  }

  private configureInChargeList(): void {
    if (this.allAuditorInchageList.length > 0) {
      this.prepareAuditorInChargeList();
    } else {
      this.prepareAuditorList();
    }
  }

  private prepareAuditorList(): void {
    let auditors = this.allAuditors.filter(item => item.role.includes(ROLE.AUDITOR));
    let auditorsList = auditors.map(item => ({ label: item.firstName, value: item.uuid, raw: item }));
    this.fields = this._formFactoryService.setOptionsToDropDown(this.fields, 'inchargeUid', auditorsList);
  }

  private prepareAuditorInChargeList(): void {
    const auditors = this.allAuditorInchageList.map(item => {
      const fullName = item.firstName
        ? item.lastName
          ? `${item.firstName} ${item.lastName}`
          : item.firstName
        : '';

      return {
        label: item.code ? `${fullName} [${item.code}]` : fullName,
        value: item.uuid
      };
    });
    this.fields = this._formFactoryService.setOptionsToDropDown(this.fields, 'inchargeUid', auditors);
  }


  //configure form
  private configureForm(): void {
    switch (this.status) {
      case TABLE_ACTION_TYPES.CREATE:
        if (environment.Partner == PARTNERS.NORLANKA) {
          this.auditorForm.get("role").enable();
        } else {
          this.auditorForm.get("role").setValue(ROLE.AUDITOR);
          this.auditorForm.get("role").disable();
        }
        this.auditorForm.get('setPasswordAsUserName').setValue(true);
        break;
      case TABLE_ACTION_TYPES.EDIT:
        this.hdr = "Update Auditor";
        this.auditorForm.get("code").disable();
        this.auditorForm.get("role").disable();
        this.setDataToForm();
        break;
      case TABLE_ACTION_TYPES.VIEW:
        this.hdr = "View Auditor";
        this.setDataToForm();
        this.auditorForm.disable();
        break;
      default:
        break;
    }
    this.onFormChanges();
  }

  private setDataToForm(): void {
    this.rec = this._dialogConfig.data?.data;
    this.auditorForm = this._formFactoryService.setFormControlsValues(this.auditorForm, this.rec, this.fields);
    this.auditorForm.get('resetPassword').setValue(false);
    if (this.rec.role.includes(ROLE.DQA)) {
      this.auditorForm.get('role').setValue(ROLE.DQA);
      this.prepareAuditorList();
    } else {
      this.auditorForm.get('role').setValue(ROLE.AUDITOR);
      this.prepareAuditorInChargeList();
    }
  }

  private onFormChanges(): void {
    this.auditorForm.get('role').valueChanges
      .pipe(distinctUntilChanged())
      .subscribe({
        next: response => {
          if (response) {
            if (response?.includes("DQA")) {
              this.prepareAuditorList();
            } else {
              this.prepareAuditorInChargeList();
            }
          }
        }
      })

    if (environment.Partner == PARTNERS.GODREJ) {
      this.auditorForm.get('setPasswordAsUserName').setValue(true);
      this.auditorForm.get('setPasswordAsUserName').disable();
    } else {
      //this.auditorForm.get('setPasswordAsUserName').setValue(false);
    }

    this.auditorForm.get('resetPassword').valueChanges
      .pipe(distinctUntilChanged())
      .subscribe({
        next: response => {
          if (response) {
            this.fields = this._formFactoryService.showHideField(this.fields, 'password', true, this.auditorForm);
          } else {
            this.fields = this._formFactoryService.showHideField(this.fields, 'password', false, this.auditorForm);
            this.hideNewPasswordField();
          }
        }
      });

    this.auditorForm.get('password').valueChanges
      .pipe(distinctUntilChanged())
      .subscribe({
        next: response => {
          if (response) {
            if (response == 'Create new password') {
              this.auditorForm.get('setPasswordAsUserName').setValue(false);
              this.showNewPasswordField();
            }
            else {
              this.auditorForm.get('setPasswordAsUserName').setValue(true);
              this.hideNewPasswordField();
            }
          }
        }
      });
  }

  private hideNewPasswordField(): void {
    this.fields = this._formFactoryService.setFieldsValidation(this.fields, "newPassword", { required: false });
    this.auditorForm.get("newPassword").clearValidators();
    this.auditorForm.get("newPassword").setValue('');
    this.auditorForm.updateValueAndValidity();
    this.fields = this._formFactoryService.showHideField(this.fields, 'newPassword', false, this.auditorForm);
  }

  private showNewPasswordField(): void {
    this.fields = this._formFactoryService.showHideField(this.fields, 'newPassword', true, this.auditorForm);
    this.fields = this._formFactoryService.setFieldsValidation(this.fields, "newPassword", { required: true });
    this.auditorForm.get("newPassword").setValidators(Validators.required);
    this.auditorForm.updateValueAndValidity();
  }

  //submit form
  public onSubmit() {
    this.rec = this._formFactoryService.mapFormFieldsDataToModel(this.auditorForm, this.rec, this.fields);
    this.rec.fullName = this.rec.firstName ? this.rec.lastName ? `${this.rec.firstName} ${this.rec.lastName}` : this.rec.firstName : '';

    if (this.rec.role = this.auditorForm.get('role').value.includes(ROLE.DQA)) {
      this.rec.role = `${ROLE.AUDITOR},${ROLE.DQA}`;
    } else {
      this.rec.role = this.auditorForm.get('role').value
    }
    if (this.rec && this.rec.uuid) {
      let password: string = this.auditorForm.get('newPassword').value;
      if (password) {
        this.rec.password = environment.Partner == PARTNERS.GODREJ ? sha512.sha512(password) : password;
      } else
        this.rec.password = sha512.sha512(this.rec.userName);
    } else {
      this.rec.password = sha512.sha512(this.rec.code);
    }

    if (this.rec.uuid) {
      this._commonService.confirm(`Are you sure you want to update this auditor?`, this.rec.fullName)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            APP_UI_CONFIG.administration.auditor.update.paramList.uuid = this.rec.uuid;
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.auditor.update, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`Auditor updated successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to update the auditor details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              });
          }
        });
    } else {
      this._commonService.confirm(`Are you sure you want to create a new auditor?`, this.rec.fullName)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.auditor.create, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`New auditor created successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to create the auditor details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              });
          }
        });
    }
  }

  //close modal
  public close(status) {
    this._dialogConfigRef.close(status);
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}