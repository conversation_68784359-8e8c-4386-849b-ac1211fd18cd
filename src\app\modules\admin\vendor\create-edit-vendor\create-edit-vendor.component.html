<div class="w-full flex flex-col gap-1">
    <div class="modal-content">
        <tps-form class="w-full" [formGroup]="vendorForm" [fields]="fields" [status]="status"
            [layout]="isMobile ? 'column':'row'">
        </tps-form>

        <!-- Country Coordinates Display -->
        <div *ngIf="selectedCountryData" class="mt-4 p-4 bg-gray-50 rounded-lg border">
            <h4 class="text-sm font-semibold text-gray-700 mb-2">
                <i class="pi pi-map-marker mr-2"></i>Country Geographic Information
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div class="flex flex-col">
                    <span class="text-gray-600 font-medium">Country:</span>
                    <span class="text-gray-800">{{ selectedCountryData.name }}</span>
                </div>
                <div class="flex flex-col">
                    <span class="text-gray-600 font-medium">Country Code:</span>
                    <span class="text-gray-800">{{ selectedCountryData.code }}</span>
                </div>
                <div class="flex flex-col">
                    <span class="text-gray-600 font-medium">Latitude:</span>
                    <span class="text-gray-800">{{ selectedCountryData.latitude }}°</span>
                </div>
                <div class="flex flex-col">
                    <span class="text-gray-600 font-medium">Longitude:</span>
                    <span class="text-gray-800">{{ selectedCountryData.longitude }}°</span>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <div class="w-full flex flex-row justify-end gap-6">
            <tps-secondary-button [buttonName]="'Cancel'" (onClick)="close(false)"></tps-secondary-button>
            <tps-primary-button *ngIf="status != TABLE_ACTION_TYPES.VIEW" [buttonName]="'Submit'"
                [isDisabled]="vendorForm.invalid" (onClick)="onSubmit()">
            </tps-primary-button>
        </div>
    </div>
</div>
