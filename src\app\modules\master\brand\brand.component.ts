import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Brand } from 'app/core/models/brand-model';
import { SharedModule } from 'app/shared/shared.module';
import {
  actionType,
  buttonActions,
  CommonService,
  HTTP_STATUS,
  ICON_BUTTON_SEVERITY,
  ICON_PRIMENG_LIST,
  InvokeService,
  TABLE_ACTION_TYPES,
  toolbarActions,
} from 'app/shared/tapas-ui';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { finalize, ReplaySubject, takeUntil } from 'rxjs';

import { CreateEditBrandComponent } from './create-edit-brand/create-edit-brand.component';

@Component({
  selector: 'tps-brand',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './brand.component.html'
})
export class BrandComponent implements OnInit, OnD<PERSON>roy {
  hdr: string = 'Brands';
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  columnDefs: any[] = [];
  brands: Brand[] = [];
  ACL_LIST = this._commonService.getACLList();
  buttonActions: buttonActions[] = [];
  toolBarActions: toolbarActions[] = [];

  constructor(
    private _commonService: CommonService,
    private _invokeService: InvokeService,
    private _dialogService: DialogService,
  ) { }

  public ngOnInit(): void {
    this.prepareTableActions();
    this.onRefresh();
  }

  private prepareTableActions(): void {
    this.buttonActions = [
      {
        name: 'Create Brand',
        icon: 'pi-plus',
        primary: true,
        isSvg: false,
        show: this.ACL_LIST.BRAND_MASTER_CREATE,
        command: () => {
          this.onOpenBrandDialog('Create Brand', new Brand(), TABLE_ACTION_TYPES.CREATE);
        }
      }
    ];
    this.toolBarActions = [];
  }

  public onRefresh(): void {
    this.getBrands();
    this.prepareTableColumns();
  }

  private prepareTableColumns(): void {
    this.columnDefs = [
      {
        headerName: "Code",
        field: "code",
        sortable: true,
      },
      {
        headerName: "Name",
        field: "name",
        sortable: true,
      },
      {
        headerName: "Sub Brand",
        field: "subBrand",
        sortable: true,
      },
      {
        headerName: "Description",
        field: "description",
        sortable: true,
      },
      {
        headerName: "Contact No",
        field: "contactNo",
        sortable: true,
      },
      {
        headerName: "Email",
        field: "email",
        sortable: true,
      },
      {
        headerName: "Status",
        field: "statusField",
        isTemplate: true,
        maxWidth: 70,
        sortable: true,
      },
      {
        headerName: "Created On",
        field: "createdTimeText",
        maxWidth: 70,
        sortable: true,
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        maxWidth: 100,
        sortable: false,
        isActionCol: true,
        actions: (params) => this.prepareTableActionIcons(params)
      }
    ];
  }

  public prepareTableActionIcons(row): actionType[] {
    let iconsList: actionType[] = [];
    iconsList.push({
      type: TABLE_ACTION_TYPES.VIEW,
      icon: ICON_PRIMENG_LIST.PI_EYE,
      title: "View",
      data: row,
      severity: ICON_BUTTON_SEVERITY.SECONDARY,
      onAction: (rowItem) => this.onOpenBrandDialog('View Brand', row, TABLE_ACTION_TYPES.VIEW)
    });

    if (this.ACL_LIST?.BRAND_UPDATE) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.EDIT,
        icon: ICON_PRIMENG_LIST.PI_PENCIL,
        title: "Edit",
        data: row,
        severity: ICON_BUTTON_SEVERITY.INFO,
        onAction: (rowItem) => this.onOpenBrandDialog('Edit Brand', row, TABLE_ACTION_TYPES.EDIT)
      });
    }

    if (this.ACL_LIST?.BRAND_DELETE) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.DELETE,
        icon: ICON_PRIMENG_LIST.PI_TRASH,
        title: "Delete",
        data: row,
        severity: ICON_BUTTON_SEVERITY.DANGER,
        onAction: (rowItem) => this.deleteBrand(row)
      });
    }

    return iconsList;
  }

  private getBrands(): void {
    this._commonService.loadingTableData.next(true);
    this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.brand.get)
      .pipe(
        takeUntil(this.$destroyed),
        finalize(() => {
          setTimeout(() => {
            this._commonService.loadingTableData.next(false);
          }, 500);
        })
      )
      .subscribe({
        next: response => {
          this.prepareBrandData(this._commonService.sortByDateLatest(response, 'createdTime', 'createdTime'));
        },
        error: error => {
          this._commonService.handleError(error);
        }
      });
  }

  private prepareBrandData(data: any[]): void {
    this.brands = data.map(item => ({
      createdTimeText: this._commonService.dateTimeFormat(item.createdTime),
      statusField: item.isActive
        ? this._commonService.prepareTag('ACTIVE', 'success')
        : this._commonService.prepareTag('INACTIVE', 'danger'),
      ...item
    }));
    this._commonService.loadingTableData.next(false);
  }

  private deleteBrand(brand: Brand): void {
    // Implement delete logic
  }

  private onOpenBrandDialog(title, row, status): void {
    const ref: DynamicDialogRef = this._dialogService.open(CreateEditBrandComponent, {
      data: { data: row, status: status },
      header: title,
      modal: true,
      width: '60%',
      closeOnEscape: false,
      dismissableMask: false,
      transitionOptions: "300ms",
      baseZIndex: 10000,
      closable: true,
      focusOnShow: false,
      style: {
        'max-height': '90vh',
        'margin-top': '2rem'
      },
      breakpoints: {
        '768px': '100vw',
        '576px': '100vw'
      }
    });
    ref.onClose.subscribe((response: any) => {
      if (response) {
        this.onRefresh();
      }
    });
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}
