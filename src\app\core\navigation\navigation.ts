import { ICON_PRIMENG_LIST } from 'app/shared/tapas-ui';

/* eslint-disable */
export const UI_CONFIGURATION = {

    stage: {
        title: 'Quality360',
        subTitle: 'Quality & Compliance Automation Platform',
        homePage: 'home',
        menu: [
            {
                id: 'home',
                title: 'Home',
                icon: ICON_PRIMENG_LIST.PI_HOME,
                type: 'basic',
                isParent: true,
                link: 'home',
                ACL_CODE: "HOME",
            },
            {
                id: 'Administration',
                title: 'Admin',
                icon: ICON_PRIMENG_LIST.PI_COG,
                isParent: true,
                type: 'collapsable',
                ACL_CODE: "ADMIN",
                children: [
                    {
                        id: 'HealthCheck',
                        title: 'Health Check',
                        icon: ICON_PRIMENG_LIST.PI_COG,
                        type: 'basic',
                        link: '/admin/health-check',
                        ACL_CODE: "ADMIN_HEALTH_CHECK",
                    },
                    {
                        id: 'Users',
                        title: 'Users',
                        icon: ICON_PRIMENG_LIST.PI_USERS,
                        type: 'basic',
                        link: '/admin/users',
                        ACL_CODE: "USERS_MASTER",
                    },
                    {
                        id: 'Sections',
                        title: 'Sections',
                        icon: ICON_PRIMENG_LIST.PI_MAP_MARKER,
                        type: 'basic',
                        link: '/admin/sections/list',
                        ACL_CODE: "ADMIN_LOCATIONS",
                    },
                    {
                        id: 'Roles',
                        title: 'Roles',
                        icon: ICON_PRIMENG_LIST.PI_COG,
                        type: 'basic',
                        link: '/admin/role',
                        ACL_CODE: "ROLE_MASTER_CREATE",
                    },
                    {
                        id: 'Brands',
                        title: 'Brands',
                        icon: ICON_PRIMENG_LIST.PI_TAG,
                        type: 'basic',
                        link: '/admin/brand',
                        ACL_CODE: "BRAND_MASTER",
                    },
                ]
            },
            {
                id: 'help-center',
                title: 'Help Center',
                icon: ICON_PRIMENG_LIST.PI_INFO_CIRCLE,
                isParent: true,
                type: 'basic',
                link: 'help-center',
                ACL_CODE: "HELP_CENTER",
            },

        ]
    },
    madura: {
        title: 'Quality360',
        subTitle: 'Quality & Compliance Automation Platform',
        homePage: 'home',
        menu: [
            {
                id: 'home',
                title: 'Home',
                icon: ICON_PRIMENG_LIST.PI_HOME,
                type: 'basic',
                isParent: true,
                link: 'home',
                ACL_CODE: "HOME",
            },
            {
                id: 'Document',
                title: 'Document',
                icon: ICON_PRIMENG_LIST.PI_COG,
                isParent: true,
                type: 'collapsable',
                ACL_CODE: "DOCUMENT",
                children: [
                    {
                        id: 'TechPark',
                        title: 'Tech Park',
                        icon: ICON_PRIMENG_LIST.PI_COG,
                        type: 'basic',
                        link: '/document/tech-park',
                        ACL_CODE: "TECH_PARK",
                    },
                ]
            },
            {
                id: 'Master',
                title: 'Master',
                icon: ICON_PRIMENG_LIST.PI_COG,
                isParent: true,
                type: 'collapsable',
                ACL_CODE: "MASTER",
                children: [
                    {
                        id: 'Auditor',
                        title: 'Auditor',
                        icon: ICON_PRIMENG_LIST.PI_COG,
                        type: 'basic',
                        link: '/master/auditor',
                        ACL_CODE: "AUDITOR_MASTER",
                    },
                    {
                        id: 'Brands',
                        title: 'Brands',
                        icon: ICON_PRIMENG_LIST.PI_TAG,
                        type: 'basic',
                        link: '/master/brand',
                        ACL_CODE: "BRAND_MASTER",
                    },
                    {
                        id: 'Category',
                        title: 'Categories',
                        icon: ICON_PRIMENG_LIST.PI_TAG,
                        type: 'basic',
                        link: '/master/category',
                        ACL_CODE: "CATEGORY_MASTER",
                    },
                    {
                        id: 'DefectType',
                        title: 'Defect Type',
                        icon: ICON_PRIMENG_LIST.PI_TAG,
                        type: 'basic',
                        link: '/master/defectType',
                        ACL_CODE: "DEFECT_TYPE_MASTER",
                    },
                    {
                        id: 'Factory',
                        title: 'Factory',
                        icon: ICON_PRIMENG_LIST.PI_TAG,
                        type: 'basic',
                        link: '/master/factory',
                        ACL_CODE: "FACTORY_MASTER",
                    },
                    {
                        id: 'ProductType',
                        title: 'Product Type',
                        icon: ICON_PRIMENG_LIST.PI_TAG,
                        type: 'basic',
                        link: '/master/productType',
                        ACL_CODE: "PRODUCT_TYPE_MASTER",
                    },

                ]
            },
            {
                id: 'Administration',
                title: 'Admin',
                icon: ICON_PRIMENG_LIST.PI_COG,
                isParent: true,
                type: 'collapsable',
                ACL_CODE: "ADMIN",
                children: [
                    {
                        id: 'Roles',
                        title: 'Roles',
                        icon: ICON_PRIMENG_LIST.PI_COG,
                        type: 'basic',
                        link: '/admin/role',
                        ACL_CODE: "ROLE_MASTER",
                    },
                    {
                        id: 'Users',
                        title: 'Users',
                        icon: ICON_PRIMENG_LIST.PI_USERS,
                        type: 'basic',
                        link: '/admin/users',
                        ACL_CODE: "USERS_MASTER",
                    },
                    {
                        id: 'Brands',
                        title: 'Brands',
                        icon: ICON_PRIMENG_LIST.PI_TAG,
                        type: 'basic',
                        link: '/admin/brand',
                        ACL_CODE: "BRAND_MASTER",
                    },
                    {
                        id: 'Category',
                        title: 'Categories',
                        icon: ICON_PRIMENG_LIST.PI_TAG,
                        type: 'basic',
                        link: '/admin/category',
                        ACL_CODE: "CATEGORY_MASTER",
                    },
                    {
                        id: 'DefectType',
                        title: 'Defect Type',
                        icon: ICON_PRIMENG_LIST.PI_TAG,
                        type: 'basic',
                        link: '/admin/defectType',
                        ACL_CODE: "DEFECT_TYPE_MASTER",
                    },
                    {
                        id: 'ProductType',
                        title: 'Product Type',
                        icon: ICON_PRIMENG_LIST.PI_TAG,
                        type: 'basic',
                        link: '/admin/productType',
                        ACL_CODE: "PRODUCT_TYPE_MASTER",
                    },
                    {
                        id: 'Factory',
                        title: 'Factory',
                        icon: ICON_PRIMENG_LIST.PI_TAG,
                        type: 'basic',
                        link: '/admin/factory',
                        ACL_CODE: "FACTORY_MASTER",
                    },
                     {
                        id: 'Technician',
                        title: 'Technician',
                        icon: ICON_PRIMENG_LIST.PI_USER,
                        type: 'basic',
                        link: '/admin/technician',
                        ACL_CODE: "ADMIN_TECHNICIAN_MASTER",
                    },
                    {
                        id: 'Vendor',
                        title: 'Vendor',
                        icon: ICON_PRIMENG_LIST.PI_BUILDING,
                        type: 'basic',
                        link: '/admin/vendor',
                        ACL_CODE: "VENDOR_MASTER",
                    },
                ]
            },
            {
                id: 'help-center',
                title: 'Help Center',
                icon: ICON_PRIMENG_LIST.PI_INFO_CIRCLE,
                isParent: true,
                type: 'basic',
                link: 'help-center',
                ACL_CODE: "HELP_CENTER",
            },

        ]
    }
}
