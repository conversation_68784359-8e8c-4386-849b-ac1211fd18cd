import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Util } from 'app/core/common/util';
import { TechPark } from 'app/core/models/tech-park-model';
import { SharedModule } from 'app/shared/shared.module';
import {
  CommonService,
  FORM_CONTROL_TYPES,
  FormFactoryService,
  HTTP_STATUS,
  InvokeService,
  TABLE_ACTION_TYPES,
} from 'app/shared/tapas-ui';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ReplaySubject, takeUntil } from 'rxjs';

import { TECH_PARK_FORM_MODEL } from './tech-park-form.model';

@Component({
  selector: 'tps-create-edit-tech-park',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './create-edit-tech-park.component.html'
})
export class CreateEditTechParkComponent implements OnInit, OnDestroy {
  techParkForm: FormGroup;
  fields: any[] = [];
  status: string;
  techPark: TechPark;
  isMobile: boolean = false;
  private rec: TechPark = new TechPark();

  //constants
  public FORM_CONTROL_TYPES = FORM_CONTROL_TYPES;
  public TABLE_ACTION_TYPES = TABLE_ACTION_TYPES;

  //unsubscribe
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);

  constructor(
    private _commonService: CommonService,
    private _dialogConfig: DynamicDialogConfig,
    public _dialogConfigRef: DynamicDialogRef,
    private _formFactoryService: FormFactoryService,
    private _invokeService: InvokeService,
  ) { }

  ngOnInit(): void {
    // Monitor screen size changes
    this._commonService.isMobile().subscribe(result => {
      this.isMobile = result.matches;
    });

    this.status = this._dialogConfig.data?.status;
    this.buildForm();
    this.configureForm();
  }

  //build form
  private buildForm() {
    const formGroupFields = this._formFactoryService.getFormControlsFields(TECH_PARK_FORM_MODEL);
    this.fields = this._formFactoryService.getFieldsList(TECH_PARK_FORM_MODEL);
    this.techParkForm = new FormGroup(formGroupFields);
  }

  //configure form
  private configureForm(): void {
    switch (this.status) {
      case TABLE_ACTION_TYPES.CREATE:
        break;
      case TABLE_ACTION_TYPES.EDIT:
        this.setDataToForm();
        break;
      case TABLE_ACTION_TYPES.VIEW: ;
        this.setDataToForm();
        this.techParkForm.disable();
        break;
      default:
        break;
    }
  }

  private setDataToForm(): void {
    this.rec = this._dialogConfig.data?.data;
    this.techParkForm = this._formFactoryService.setFormControlsValues(this.techParkForm, this.rec, this.fields);
  }

  //submit form
  public onSubmit() {
    this.rec = this._formFactoryService.mapFormFieldsDataToModel(this.techParkForm, this.rec, this.fields);
    if (this.rec.uuid) {
      this._commonService.confirm(`Are you sure you want to update this tech park?`, this.rec.name)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            APP_UI_CONFIG.administration.brand.update.paramList.uuid = this.rec.uuid;
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.brand.update, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`Tech park updated successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to update the tech park details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              })
          }
        })
    } else {
      this._commonService.confirm(`Are you sure you want to create a new tech park?`, this.rec.name)
        .pipe(takeUntil(this.$destroyed))
        .subscribe(response => {
          if (response) {
            this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.brand.create, '', Util.clone(this.rec))
              .pipe(takeUntil(this.$destroyed))
              .subscribe({
                next: response => {
                  if (response.code == HTTP_STATUS.SUCCESS) {
                    this._commonService.success(`New tech park created successfully`);
                    this.close(true);
                  } else if (response.code == 400) {
                    this._commonService.error(response.message);
                  }
                  else {
                    this._commonService.error('Failed to create the tech park details');
                  }
                }, error: error => {
                  this._commonService.handleError(error);
                }
              })
          }
        })
    }
  }

  //close modal
  public close(status) {
    this._dialogConfigRef.close(status);
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}