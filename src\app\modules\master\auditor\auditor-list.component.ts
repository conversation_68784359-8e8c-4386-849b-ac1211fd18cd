import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Auditor } from 'app/core/models/auditor.model';
import { SharedModule } from 'app/shared/shared.module';
import {
  actionType,
  buttonActions,
  CommonService,
  ICON_BUTTON_SEVERITY,
  ICON_PRIMENG_LIST,
  InvokeService,
  TABLE_ACTION_TYPES,
  toolbarActions,
} from 'app/shared/tapas-ui';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { finalize, ReplaySubject, takeUntil } from 'rxjs';

import { CreateEditAuditorComponent } from './create-edit-auditor/create-edit-auditor.component';



@Component({
  selector: 'tps-auditor-list',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './auditor-list.component.html'
})
export class AuditorListComponent implements OnInit, On<PERSON><PERSON>roy {
  hdr: string = 'Auditors';
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  columnDefs: any[] = [];
  auditors: Auditor[] = [];
  auditorInChargeList: any[] = [];
  ACL_LIST = this._commonService.getACLList();
  buttonActions: buttonActions[] = [];
  toolBarActions: toolbarActions[] = [];
  isMobile: boolean = false;

  constructor(
    private _commonService: CommonService,
    private _invokeService: InvokeService,
    private _dialogService: DialogService,
  ) { }

  public ngOnInit(): void {
    // Monitor screen size changes
    this._commonService.isMobile().subscribe(result => {
      this.isMobile = result.matches;
    });

    this.prepareTableActions();
    this.onRefresh();
  }

  private prepareTableActions(): void {
    this.buttonActions = [
      {
        name: 'Create Auditor',
        icon: 'pi-plus',
        primary: true,
        isSvg: false,
        show: this.ACL_LIST.AUDITOR_CREATE,
        command: () => {
          this.onOpenAuditorDialog('Create Auditor', new Auditor(), TABLE_ACTION_TYPES.CREATE);
        }
      }
    ];
    this.toolBarActions = [];
  }

  public onRefresh(): void {
    this.getAuditors();
    this.prepareTableColumns();
  }

  private prepareTableColumns(): void {
    this.columnDefs = [
      {
        headerName: "Code",
        field: "code",
        sortable: true,
      },
      {
        headerName: "Full Name",
        field: "fullName",
        sortable: true,
      },
      {
        headerName: "Designation",
        field: "designation",
        sortable: true,
      },
      {
        headerName: "Role",
        field: "role",
        sortable: true,
      },
      {
        headerName: "In-Charge",
        field: "inChargeName",
        sortable: true,
      },
      {
        headerName: "Email",
        field: "email",
        sortable: true,
      },
      {
        headerName: "Contact No",
        field: "contactNo",
        sortable: true,
      },
      {
        headerName: "City",
        field: "city",
        sortable: true,
      },
      {
        headerName: 'Actions',
        pinned: 'right',
        maxWidth: 100,
        sortable: false,
        isActionCol: true,
        actions: (params) => this.prepareTableActionIcons(params)
      }
    ];
  }

  public prepareTableActionIcons(row): actionType[] {
    let iconsList: actionType[] = [];
    iconsList.push({
      type: TABLE_ACTION_TYPES.VIEW,
      icon: ICON_PRIMENG_LIST.PI_EYE,
      title: "View",
      data: row,
      severity: ICON_BUTTON_SEVERITY.SECONDARY,
      onAction: (rowItem) => this.onOpenAuditorDialog('View Auditor', row, TABLE_ACTION_TYPES.VIEW)
    });

    if (this.ACL_LIST?.AUDITOR_UPDATE) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.EDIT,
        icon: ICON_PRIMENG_LIST.PI_PENCIL,
        title: "Edit",
        data: row,
        severity: ICON_BUTTON_SEVERITY.INFO,
        onAction: (rowItem) => this.onOpenAuditorDialog('Edit Auditor', row, TABLE_ACTION_TYPES.EDIT)
      });
    }

    if (row.isActive == 0 && this.ACL_LIST?.AUDITOR_ACTIVATE) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.ACTIVATE,
        icon: ICON_PRIMENG_LIST.PI_POWER_OFF,
        title: "Activate",
        data: row,
        severity: ICON_BUTTON_SEVERITY.SUCCESS,
        onAction: (rowItem) => this.onActivate(row)
      });
    }
    if (row.isActive == 1 && this.ACL_LIST?.AUDITOR_DEACTIVATE) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.DEACTIVATE,
        icon: ICON_PRIMENG_LIST.PI_POWER_OFF,
        title: "De-Activate",
        data: row,
        severity: ICON_BUTTON_SEVERITY.DANGER,
        onAction: (rowItem) => this.onDeActivate(row)
      });
    }
    if (this.ACL_LIST?.AUDITOR_DELETE) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.DELETE,
        icon: ICON_PRIMENG_LIST.PI_TRASH,
        title: "Delete",
        data: row,
        severity: ICON_BUTTON_SEVERITY.DANGER,
        onAction: (rowItem) => this.onDeleteAuditor(row)
      });
    }


    return iconsList;
  }

  private getAuditors(): void {
    this._commonService.loadingTableData.next(true);
    this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.auditor.getAllAuditors)
      .pipe(
        takeUntil(this.$destroyed),
        finalize(() => {
          setTimeout(() => {
            this._commonService.loadingTableData.next(false);
          }, 500);
        })
      )
      .subscribe({
        next: response => {
          this.getAuditorInCharge(response);
        },
        error: error => {
          this._commonService.handleError(error);
        }
      });
  }

  private getAuditorInCharge(auditors): void {
    this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.auditorInCharge.getAllAuditorInCharge)
      .pipe(takeUntil(this.$destroyed))
      .subscribe({
        next: response => {
          this.auditorInChargeList = response;
          this.prepareAuditorData(auditors);
        },
        error: error => {
          this._commonService.handleError(error);
        }
      });
  }

  private prepareAuditorData(auditors: any[]): void {
    this.auditors = auditors.map(item => {
      let inChargeItem = this.auditorInChargeList.find(inchItem => inchItem.uuid == item.inchargeUid);
      return { inChargeName: inChargeItem?.fullName || this.getInchargeName(auditors, item), ...item }
    });
    this._commonService.loadingTableData.next(false);
  }
  private getInchargeName(auditors, auditItem): string {
    return auditors.find(item => item.uuid == auditItem.inchargeUid)?.fullName;
  }

  private onDeActivate(row: Auditor): void {
    this._commonService.confirm('Are you sure you want to de-activate this auditor?', row.fullName)
      .pipe(takeUntil(this.$destroyed))
      .subscribe(confirm => {
        if (confirm) {
          this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.auditor.deActivate, row.uuid)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: response => {
                this._commonService.success('Auditor de-activated successfully');
                this.onRefresh();
              },
              error: error => {
                this._commonService.handleError(error);
              }
            });
        }
      });
  }

  private onActivate(row: Auditor): void {
    this._commonService.confirm('Are you sure you want to activate this auditor?', row.fullName)
      .pipe(takeUntil(this.$destroyed))
      .subscribe(confirm => {
        if (confirm) {
          this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.auditor.activate, row.uuid)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: response => {
                this._commonService.success('Auditor activated successfully');
                this.onRefresh();
              },
              error: error => {
                this._commonService.handleError(error);
              }
            });
        }
      });
  }
  private onDeleteAuditor(row: Auditor): void {
    this._commonService.confirm('Are you sure you want to delete this auditor?', row.fullName)
      .pipe(takeUntil(this.$destroyed))
      .subscribe(confirm => {
        if (confirm) {
          this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.auditor.delete, row.uuid)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: response => {
                this._commonService.success('Auditor deleted successfully');
                this.onRefresh();
              },
              error: error => {
                this._commonService.handleError(error);
              }
            });
        }
      });
  }

  private onOpenAuditorDialog(title, row, status): void {
    const ref: DynamicDialogRef = this._dialogService.open(CreateEditAuditorComponent, {
      data: { data: row, status: status },
      header: title,
      modal: true,
      width: this.isMobile ? '100%' : '60%',
      closeOnEscape: false,
      dismissableMask: false,
      transitionOptions: "300ms",
      baseZIndex: 10000,
      closable: true,
      focusOnShow: false,
      style: {
        'max-height': '90vh',
        'margin-top': this.isMobile ? '0' : '2rem'
      },
      breakpoints: {
        '768px': '100vw',
        '576px': '100vw'
      }
    });
    ref.onClose.subscribe((response: any) => {
      if (response) {
        this.onRefresh();
      }
    });
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}