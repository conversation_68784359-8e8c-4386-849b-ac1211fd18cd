import { FORM_CONTROL_TYPES, FORM_FIELDS_CONSTANTS_VALUES, ICON_BUTTON_COLOR } from 'app/shared/tapas-ui';

export const TECHNICIAN_FORM_MODEL = {
    code: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Code",
        placeholder: 'Ex. TECH001',
        topGroupTitleIcon: 'pi-user',
        severity: ICON_BUTTON_COLOR.WARNING,
        topGroupTitle: "Technician Details",
        show: true,
        rules: {
            required: true,
            maxLength: 250,
        }
    },
    firstName: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "First Name",
        placeholder: 'Ex. John',
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_WITH_SPACE,
            maxLength: 250,
        }
    },
    lastName: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Last Name",
        placeholder: 'Ex. Doe',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_WITH_SPACE,
            maxLength: 250,
        }
    },
    reportsTo: {
        type: FORM_CONTROL_TYPES.SINGLE_SELECT,
        onChange: (event) => { },
        options: [],
        value: "",
        label: "Reports To",
        placeholder: 'Select an In-Charge',
        show: true,
        rules: {
            // Not required
        }
    },
    contactNo: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Contact Num",
        placeholder: 'Ex. +1234567890',
        topGroupTitle: 'Contact Details',
        topGroupTitleIcon: 'pi-phone',
        severity: ICON_BUTTON_COLOR.WARNING,
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,
            maxLength: 15,
        }
    },
    email: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "EMail",
        placeholder: 'Ex. <EMAIL>',
        show: true,
        rules: {
            required: true,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.EMAIL_PATTERN,
            maxLength: 250,
        }
    },
    addressLine1: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "Address",
        placeholder: 'Ex. 123 Main Street',
        topGroupTitle: 'Address Details',
        topGroupTitleIcon: 'pi-map-marker',
        severity: ICON_BUTTON_COLOR.WARNING,
        show: true,
        rules: {
            maxLength: 250,
        }
    },
    city: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "City",
        placeholder: 'Ex. New York',
        show: true,
        rules: {
            maxLength: 250,
            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_PATTERN,
        }
    },
    pincode: {
        type: FORM_CONTROL_TYPES.TEXT,
        value: "",
        label: "PIN",
        placeholder: 'Ex. 123456',
        show: true,
        rules: {
            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,
            maxLength: 10,
        }
    }
}
