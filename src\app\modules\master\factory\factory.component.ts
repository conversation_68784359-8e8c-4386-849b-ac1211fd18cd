import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { APP_UI_CONFIG } from 'app/app-config.constants';
import { Factory } from 'app/core/models/factory.model';
import { SharedModule } from 'app/shared/shared.module';
import {
  actionType,
  buttonActions,
  CommonService,
  ICON_BUTTON_SEVERITY,
  ICON_PRIMENG_LIST,
  InvokeService,
  TABLE_ACTION_TYPES,
  toolbarActions,
} from 'app/shared/tapas-ui';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { finalize, ReplaySubject, takeUntil } from 'rxjs';
import { CreateEditFactoryComponent } from './create-edit-factory/create-edit-factory.component';

@Component({
  selector: 'tps-factory',
  templateUrl: './factory.component.html',
  standalone: true,
  imports: [SharedModule],
})
export class FactoryComponent implements OnInit, OnDestroy {
  hdr: string = 'Factories';
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  columnDefs: any[] = [];
  factories: Factory[] = [];
  ACL_LIST = this._commonService.getACLList();
  buttonActions: buttonActions[] = [];
  toolBarActions: toolbarActions[] = [];
  isMobile: boolean = false;

  constructor(
    private _commonService: CommonService,
    private _invokeService: InvokeService,
    private _dialogService: DialogService,
  ) { }

  public ngOnInit(): void {
    // Monitor screen size changes
    this._commonService.isMobile().subscribe(result => {
      this.isMobile = result.matches;
    });
    this.prepareTableActions();
    this.onRefresh();
  }

  private prepareTableActions(): void {
    this.buttonActions = [
      {
        name: 'Create Factory',
        icon: 'pi-plus',
        primary: true,
        isSvg: false,
        show: this.ACL_LIST.FACTORY_MASTER_CREATE,
        command: () => {
          this.onOpenFactoryDialog('Create Factory', new Factory(), TABLE_ACTION_TYPES.CREATE);
        }
      }
    ];
    this.toolBarActions = [];
  }

  public onRefresh(): void {
    this.getFactories();
    this.prepareTableColumns();
  }

  private prepareTableColumns(): void {
    this.columnDefs = [
      {
        headerName: "Code",
        field: "code",
        sortable: true,
      },
      {
        headerName: "Name",
        field: "name",
        sortable: true,
      },
      {
        headerName: "Address",
        field: "addressLine1",
        sortable: true,
      },
      {
        headerName: "City",
        field: "city",
        sortable: true,
      },
      {
        headerName: "Pin Code",
        field: "pincode",
        sortable: true,
      },
      {
        headerName: "Country",
        field: "country",
        sortable: true,
      },
      {
        headerName: "Audit Score",
        field: "factoryAuditScore",
        sortable: true,
      },
      {
        headerName: "Risk Level",
        field: "riskLevel",
        sortable: true,
      },
      {
        headerName: "Valid Upto",
        field: "validUptoText",
        sortable: true,
      },
      // {
      //   headerName: "Created Date",
      //   field: "createdDateText",
      //   sortable: true,
      // },
      {
        headerName: 'Actions',
        pinned: 'right',
        maxWidth: 100,
        sortable: false,
        isActionCol: true,
        actions: (params) => this.prepareTableActionIcons(params)
      }
    ];
  }

  public prepareTableActionIcons(row): actionType[] {
    let iconsList: actionType[] = [];
    iconsList.push({
      type: TABLE_ACTION_TYPES.VIEW,
      icon: ICON_PRIMENG_LIST.PI_EYE,
      title: "View",
      data: row,
      severity: ICON_BUTTON_SEVERITY.SECONDARY,
      onAction: (rowItem) => this.onOpenFactoryDialog('View Factory', row, TABLE_ACTION_TYPES.VIEW)
    });

    if (this.ACL_LIST?.FACTORY_MASTER_EDIT) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.EDIT,
        icon: ICON_PRIMENG_LIST.PI_PENCIL,
        title: "Edit",
        data: row,
        severity: ICON_BUTTON_SEVERITY.INFO,
        onAction: (rowItem) => this.onOpenFactoryDialog('Edit Factory', row, TABLE_ACTION_TYPES.EDIT)
      });
    }

    if (this.ACL_LIST?.FACTORY_MASTER_DELETE) {
      iconsList.push({
        type: TABLE_ACTION_TYPES.DELETE,
        icon: ICON_PRIMENG_LIST.PI_TRASH,
        title: "Delete",
        data: row,
        severity: ICON_BUTTON_SEVERITY.DANGER,
        onAction: (rowItem) => this.deleteFactory(row)
      });
    }

    return iconsList;
  }

  private getFactories(): void {
    this._commonService.loadingTableData.next(true);
    this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.factory.get)
      .pipe(
        takeUntil(this.$destroyed),
        finalize(() => {
          setTimeout(() => {
            this._commonService.loadingTableData.next(false);
          }, 500);
        })
      )
      .subscribe({
        next: response => {
          this.prepareFactoryData(this._commonService.sortByDateLatest(response, 'createdDate', 'createdDate'));
        },
        error: error => {
          this._commonService.handleError(error);
        }
      });
  }

  private prepareFactoryData(data: any[]): void {
    this.factories = data.map(item => ({
      createdDateText: this._commonService.dateTimeFormat(item.createdDate),
      validUptoText: this._commonService.dateTimeFormat(item.validUpto),
      ...item
    }));
  }

  private deleteFactory(row): void {
    this._commonService.confirm(`Are you sure you want to delete this factory?`, row.name)
      .pipe(takeUntil(this.$destroyed))
      .subscribe(response => {
        if (response) {
          this._invokeService.serviceInvocation(APP_UI_CONFIG.administration.factory.delete, row.id)
            .pipe(takeUntil(this.$destroyed))
            .subscribe({
              next: response => {
                this._commonService.success(`Factory deleted successfully`);
                this.onRefresh();
              },
              error: error => {
                this._commonService.handleError(error);
              }
            });
        }
      });
  }

  private onOpenFactoryDialog(title, row, status): void {
    const ref: DynamicDialogRef = this._dialogService.open(CreateEditFactoryComponent, {
      data: { data: row, status: status },
      header: title,
      modal: true,
      width: this.isMobile ? '100%' : '60%',
      closeOnEscape: false,
      dismissableMask: false,
      transitionOptions: "300ms",
      baseZIndex: 10000,
      closable: true,
      focusOnShow: false,
      style: {
        'max-height': '90vh',
        'margin-top': this.isMobile ? '0' : '2rem'
      },
      breakpoints: {
        '768px': '100vw',
        '576px': '100vw'
      }
    });
    ref.onClose.subscribe((response: any) => {
      if (response) {
        this.onRefresh();
      }
    });
  }

  public ngOnDestroy(): void {
    this.$destroyed.next(null);
    this.$destroyed.complete();
  }
}